import logger from '@lcs/logger'
import { prettifyError, z } from 'zod'
import { getErrorMessage } from '@tess-f/backend-utils'
import { EventType } from '@tess-f/shared-config'
import { 
  SubmitAssessmentRequest, 
  SubmitAssessmentResponse, 
  submitAssessmentRequestSchema
} from '../../models/internal/submit-assessment.js'
import { SessionModel } from '../../models/session.model.js'
import QuestionResponseModel from '../../models/question-response.model.js'
import updateEvaluationSession from '../mssql/session/update.service.js'
import getEvaluationSession from '../mssql/session/get.service.js'
import createQuestionResponse from '../mssql/question-response/create.service.js'
import getEvaluation from '../mssql/evaluations/get.service.js'
import getAllQuestionsForEvaluation from '../mssql/questions/get-all-for-evaluation.service.js'
import gradeQuestion from './auto-grade-service.js'
import mssql from '@lcs/mssql-utility'
import { SessionQuestionScore, SessionQuestionScoreFields, EvaluationSectionTableName as SessionQuestionScoreTableName } from '@tess-f/sql-tables/dist/evaluations/session-question-score.js'
import { QuestionResponse } from '@tess-f/sql-tables/dist/evaluations/question-response.js'

const log = logger.create('submit-assessment-service')

/**
 * Converts an QuestionResponse to a QuestionResponseModel
 */
function createQuestionResponseModel(
  sessionId: string,
  response: QuestionResponse
): QuestionResponseModel {
  return new QuestionResponseModel({
    SessionId: sessionId,
    QuestionId: response.QuestionId,
    QuestionVersion: response.QuestionVersion,
    OptionId: response.OptionId,
    OptionVersion: response.OptionVersion,
    TargetOptionId: response.TargetOptionId,
    TargetOptionVersion: response.TargetOptionVersion,
    ResponseText: response.ResponseText,
    Duration: response.Duration,
    OrderId: response.OrderId
  })
}

/**
 * Groups responses by question ID and version
 */
function groupResponsesByQuestion(
  responses: QuestionResponse[]
): Map<string, QuestionResponse[]> {
  const grouped = new Map<string, QuestionResponse[]>()
  
  for (const response of responses) {
    const key = `${response.QuestionId}-${response.QuestionVersion}`
    if (!grouped.has(key)) {
      grouped.set(key, [])
    }
    grouped.get(key)!.push(response)
  }
  
  return grouped
}

/**
 * Calculates total score and determines pass/fail status
 */
async function calculateSessionResults(sessionId: string): Promise<{
  totalScore: number
  maxScore: number
  passed: boolean
  gradedCount: number
  pendingCount: number
}> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)

  const results = await request.query<SessionQuestionScore>(`
    SELECT
      [${SessionQuestionScoreFields.Score}],
      [${SessionQuestionScoreFields.Pending}]
    FROM [${SessionQuestionScoreTableName}]
    WHERE [${SessionQuestionScoreFields.SessionId}] = @sessionId
  `)

  let totalScore = 0
  let gradedCount = 0
  let pendingCount = 0

  for (const score of results.recordset) {
    if (score.Pending) {
      pendingCount++
    } else {
      // Round partial scores to the nearest whole number
      const roundedScore = Math.round(score.Score || 0)
      totalScore += roundedScore
      gradedCount++
    }
  }

  // For maxScore, we need to get the total possible points from the evaluation questions
  // This is a simplified calculation - in practice you might want to get this from the evaluation
  const maxScore = gradedCount + pendingCount // Assuming 1 point per question as default

  // Simple pass/fail logic - pass if score is >= 70% (this should be configurable per evaluation)
  const passed = maxScore > 0 ? (totalScore / maxScore) >= 0.7 : false

  return {
    totalScore,
    maxScore,
    passed,
    gradedCount,
    pendingCount
  }
}

/**
 * Handles validation errors and creates user-friendly error messages
 */
function handleValidationError(error: z.ZodError, request: SubmitAssessmentRequest): never {
  const errorMessage = prettifyError(error)
  log('warn', 'Invalid request data for submit assessment', {
    errorMessage,
    sessionId: request.sessionId
  })
  throw new Error(`Invalid request data: ${errorMessage}`)
}

/**
 * Submits a completed assessment with student responses.
 * This service handles the complete submission workflow:
 * 1. Validates the submission request
 * 2. Saves all student responses to the database
 * 3. Grades all questions automatically where possible
 * 4. Calculates total score and pass/fail status
 * 5. Updates the session with completion data
 * 
 * @param request The assessment submission data
 * @returns Promise containing submission results and scoring information
 */
export default async function submitAssessment(
  request: SubmitAssessmentRequest
): Promise<SubmitAssessmentResponse> {
  try {
    // 1. Validate input
    const validatedRequest = submitAssessmentRequestSchema.parse(request)

    const { sessionId, responses, endTime } = validatedRequest

    log('info', 'Starting assessment submission process', {
      sessionId,
      responsesCount: responses.length,
      eventType: EventType.evaluation_create
    })

    // 2. Get session and evaluation data
    const session = await getEvaluationSession(sessionId)
    const evaluation = await getEvaluation(session.fields.EvalId!)
    const evaluationQuestions = await getAllQuestionsForEvaluation(
      evaluation.Id!, 
      evaluation.Version!
    )

    // 3. Group responses by question for processing
    const responsesByQuestion = groupResponsesByQuestion(responses)

    log('info', 'Processing question responses', {
      sessionId,
      uniqueQuestionsCount: responsesByQuestion.size,
      eventType: EventType.question_grade
    })

    // 4. Save responses and grade questions
    let processedResponses = 0
    for (const [questionKey, questionResponses] of responsesByQuestion) {
      const [questionId, questionVersionStr] = questionKey.split('-')
      const questionVersion = Number.parseInt(questionVersionStr)
      
      // Find the question definition
      const question = evaluationQuestions.find(q => 
        q.Id === questionId && q.Version === questionVersion
      )
      
      if (!question) {
        log('warn', 'Question not found in evaluation', {
          questionId,
          questionVersion,
          sessionId,
          eventType: EventType.question_grade
        })
        continue
      }

      // Convert to QuestionResponseModel instances and save
      const responseModels = questionResponses.map(resp => 
        createQuestionResponseModel(sessionId, resp)
      )
      
      // Save responses to database
      for (const responseModel of responseModels) {
        await createQuestionResponse(responseModel)
      }
      
      // Grade the question
      await gradeQuestion(question, responseModels, sessionId, evaluation.Id, evaluation.Version)
      processedResponses += questionResponses.length
    }

    // 5. Calculate final results
    const results = await calculateSessionResults(sessionId)

    // 6. Update session with completion data
    await updateEvaluationSession(new SessionModel({
      Id: sessionId,
      End: endTime,
      Score: results.totalScore,
      Passed: results.passed
    }))

    // 7. Return response
    const response: SubmitAssessmentResponse = {
      sessionId,
      passed: results.passed,
      totalScore: results.totalScore,
      maxScore: results.maxScore,
      responsesCount: processedResponses,
      gradedQuestionsCount: results.gradedCount,
      pendingQuestionsCount: results.pendingCount
    }

    log('info', 'Successfully submitted assessment', {
      response,
      eventType: EventType.evaluation_create
    })
    
    return response

  } catch (error) {
    // Handle errors
    if (error instanceof z.ZodError) {
      handleValidationError(error, request)
    }

    log('error', 'Failed to submit assessment', {
      error: getErrorMessage(error),
      sessionId: request.sessionId,
      eventType: EventType.evaluation_create
    })
    throw error
  }
}
